CREATE OR REPLACE FUNCTION public.f_regist_update_display_field_list(
    in_field_mapping_no bigint,
    in_tenant_no bigint,
    in_window_id character varying,
    in_language_code character varying,
    in_field_no bigint[],
    in_display_area character varying,
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

-- 項目マッピングマスタを登録・更新する
-- Parameters
 -- @param in_field_mapping_no bigint
 -- @param in_tenant_no bigint
 -- @param in_window_id character varying
 -- @param in_language_code character varying
 -- @param in_field_no bigint[]
 -- @param in_display_area character varying
----------------------------------------------------------------------------------------------------
BEGIN

    WITH
    -- 更新
    exist_field_mapping_data AS (
        UPDATE m_field_mapping fm
        SET field_no = in_field_no,
            display_area = in_display_area,
            update_datetime = now()
        WHERE fm.field_mapping_no = in_field_mapping_no
        AND fm.tenant_no = in_tenant_no
        RETURNING
            fm.field_mapping_no
    ),

    -- 新規登録
    new_field_mapping_data AS (
        INSERT INTO m_field_mapping (
            tenant_no,
            window_id,
            language_code,
            field_no,
            display_area,
            create_datetime,
            update_datetime,
            delete_flag
        )
        (
            SELECT
                in_tenant_no,
                in_window_id,
                in_language_code,
                in_field_no,
                in_display_area,
                now(),
                now(),
                0
            WHERE
                in_field_mapping_no IS NULL
        )
        RETURNING
            field_mapping_no
    )

    SELECT 200 INTO status FROM exist_field_mapping_data, new_field_mapping_data;
    result := true;
    status := 200;
    message := '';

RETURN NEXT;

EXCEPTION

    --その他エラー
    WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
